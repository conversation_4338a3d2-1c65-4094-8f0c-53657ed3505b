/*
 * Copyright [2025] MINIEYE
 * Description : 编译时加密的RSA密钥数据
 * Author      : x<PERSON><PERSON><PERSON><PERSON> (auto-generated)
 * Date        : 2025-08-08
 */

#pragma once

#include "CompileTimeEncryption.h"

namespace minieye {
namespace mlog {

// 编译时加密的RSA私钥
constexpr char PRIVATE_KEY_SOURCE[] = R"(-----B<PERSON>IN PRIVATE KEY-----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-----END PRIVATE KEY-----
)";

// 编译时加密的RSA公钥  
constexpr char PUBLIC_KEY_SOURCE[] = R"(-----BEGIN PUBLIC KEY-----
MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAzn8MUgKNwD/qwX9ocpEA
M9i3f5GXMS7zcD+G8yw7y9fiNUQ8obRd+bHQFKOgqBVDDer3nALh2raM5rpPeVui
1b1/t9c9zfbkS12xrNaDwxM9SQ70hpg+D2cRxx0iC3FOO6djak99G8ltxOb5txDm
dpP+Q+DiEE/7/BAxvIjJc5goI+mVCmNIRnSOIQj78qEc37P4YMvxim4gk7j8MRjw
luci/ShD04rTc+AW8G9XvXMJxZiYRTx0HzgS37csySllpObFUXFXtM2yOF3Rzsj3
Bn28HN1mLJcsTDE20v2CpFc63GG2a+xyPxcZLy6L5iilrx++nMieqjy+RXniBzl6
8MGdP+8wRIWvNPhDgBFiN25kqW+MYqh9SXn77RUjoUj3omZ8lzESapEF7LvMaZx9
uPyIoEU/nXRpHX7vPp1ZJxW2ASQI6HOpACrXJMwwwYAAXdlJUIYO+dwWObFZeD0F
Gq2klg3ujTw0KRgdOvHx50xhtXM1scLhNbvw45qtW6QLAgMBAAE=
-----END PUBLIC KEY-----
)";

// 编译时加密处理
constexpr auto ENCRYPTED_PRIVATE_KEY = CompileTimeEncryption::encrypt(PRIVATE_KEY_SOURCE);
constexpr auto ENCRYPTED_PUBLIC_KEY = CompileTimeEncryption::encrypt(PUBLIC_KEY_SOURCE);

}  // namespace mlog
}  // namespace minieye
