/*
 * Copyright [2025] MINIEYE
 * Description : RSA密钥数据
 * Author      : xu<PERSON><PERSON>n
 * Date        : 2025-08-09
 */

#pragma once

#include "RSAKeyEncryption.h"

namespace minieye {
namespace mlog {

#if !defined(_DEBUG) && !defined(__OPTIMIZE__)
#pragma message "[WARNING] 当前编译未开启优化，RSA密钥会以明文形式编译进二进制文件!"
#endif

// 编译时加密的RSA私钥
constexpr char PRIVATE_KEY_SOURCE[] = R"(***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)";

// 编译时加密的RSA公钥
constexpr char PUBLIC_KEY_SOURCE[] = R"(-----BEGIN PUBLIC KEY-----
MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAjpiu9InR94MGVzzE//xa
PTB6OgyzAoweUbAUC7JaetvgdwzSocrqcIDcJCmRFFrurx/vYH6JoJRnfDxuDHOO
zOuBnH+XWPgIUwSJcoqWCVLoA9XYD69iTogGTYTeZrRl2bO3dxvZHYpS529W2YCO
pqYdTj8dYoSyoqzWl0I4Lb/pCMoBi7V4xhfvqmlImD+Ucwk5UvtKVQR2tZokO3rD
wqlHaJdw+eqf7lspKxPZy/fG/99WE9ru32NqLAYptUkvucevQ1opnkn+gsK5R3WL
WGTxqWacQeMZCaCz0zBDdfqOeCtnGaXSG1N+BX8CHjVdU63aZOLHayYQ3WkchLTn
mkTrc1+dXSV1Pg2bXQffU+zlbtj/lap3c1MHMhEM3XjAT7rcJnummriQYdcw6t0v
RYF/+HK6KVDTdGG4nSet9imIw4qBnT342o9/b5EZ+OXKcv35aMQge2s5qb5MBV3b
IqCZKQnRdGbnpljgw9dkYm9zcYcgMxKxPJbtpuFAQiVJAgMBAAE=
-----END PUBLIC KEY-----
)";

// 临时禁用编译时加密，用于测试
// constexpr auto ENCRYPTED_PRIVATE_KEY = CompileTimeEncryption::encrypt(PRIVATE_KEY_SOURCE);
// constexpr auto ENCRYPTED_PUBLIC_KEY = CompileTimeEncryption::encrypt(PUBLIC_KEY_SOURCE);

}  // namespace mlog
}  // namespace minieye
