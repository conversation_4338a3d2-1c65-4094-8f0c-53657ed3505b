/*
 * Copyright [2025] MINIEYE
 * Description : Log混淆器
 * Author      : x<PERSON><PERSON><PERSON>n
 * Date        : 2025-08-06
 */

#include "LogObfuscator.h"
#include "MiniRSA.h"
#include "EncryptedRSAKeys.h"
#include <algorithm>
#include <stdexcept>
#include <cstring>
#include <sstream>

namespace minieye {
namespace mlog {

std::string LogObfuscator::obfuscate(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    try {
        // Parse RSA public key
        std::string publicKeyPEM = getPublicKeyPEM();
        if (publicKeyPEM.empty()) {
            fprintf(stderr, "DEBUG: Public key PEM is empty\n");
            return input;
        }



        RSAKey publicKey = MiniRSA::parseKeyFromPEM(publicKeyPEM, false);

        // Split input into chunks
        std::vector<std::string> chunks = splitIntoChunks(input, RSA_CHUNK_SIZE);
        std::vector<std::string> encryptedChunks;

        for (const auto& chunk : chunks) {
            // Convert chunk to bytes
            std::vector<uint8_t> chunkData(chunk.begin(), chunk.end());

            // Encrypt chunk
            std::vector<uint8_t> encrypted = MiniRSA::encrypt(chunkData, publicKey);

            // Encode to Base64
            std::string base64Encrypted = base64Encode(encrypted);
            encryptedChunks.push_back(base64Encrypted);
        }

        return joinChunks(encryptedChunks);
    } catch (const std::exception&) {
        // If encryption fails, return original input
        return input;
    }
}

std::string LogObfuscator::deobfuscate(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    try {
        // Parse RSA private key
        RSAKey privateKey = MiniRSA::parseKeyFromPEM(getPrivateKeyPEM(), true);

        // Split input by chunk separator
        std::vector<std::string> encryptedChunks;
        std::stringstream ss(input);
        std::string chunk;

        while (std::getline(ss, chunk, CHUNK_SEPARATOR)) {
            if (!chunk.empty()) {
                encryptedChunks.push_back(chunk);
            }
        }

        std::string result;
        for (const auto& encryptedChunk : encryptedChunks) {
            // Decode from Base64
            std::vector<uint8_t> decoded = MiniRSA::base64Decode(encryptedChunk);

            // Decrypt chunk
            std::vector<uint8_t> decrypted = MiniRSA::decrypt(decoded, privateKey);

            // Convert to string and append
            result.append(decrypted.begin(), decrypted.end());
        }

        return result;
    } catch (const std::exception&) {
        // If deobfuscation fails, return original input
        return input;
    }
}

std::string LogObfuscator::getPrivateKeyPEM() {
    // 运行时解密编译时加密的私钥
    return CompileTimeEncryption::decrypt(
        ENCRYPTED_PRIVATE_KEY.data(),
        ENCRYPTED_PRIVATE_KEY.size()
    );
}

std::string LogObfuscator::getPublicKeyPEM() {
    // 运行时解密编译时加密的公钥
    return CompileTimeEncryption::decrypt(
        ENCRYPTED_PUBLIC_KEY.data(),
        ENCRYPTED_PUBLIC_KEY.size()
    );
}

std::vector<std::string> LogObfuscator::splitIntoChunks(const std::string& input, size_t chunkSize) {
    std::vector<std::string> chunks;

    for (size_t i = 0; i < input.length(); i += chunkSize) {
        size_t currentChunkSize = std::min(chunkSize, input.length() - i);
        chunks.push_back(input.substr(i, currentChunkSize));
    }

    return chunks;
}

std::string LogObfuscator::joinChunks(const std::vector<std::string>& chunks) {
    std::string result;

    for (size_t i = 0; i < chunks.size(); ++i) {
        if (i > 0) {
            result += CHUNK_SEPARATOR;
        }
        result += chunks[i];
    }

    return result;
}

std::string LogObfuscator::base64Encode(const std::vector<uint8_t>& data) {
    static const char chars[] =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    std::string result;
    result.reserve(((data.size() + 2) / 3) * 4);

    for (size_t i = 0; i < data.size(); i += 3) {
        uint32_t value = 0;
        int count = 0;

        for (int j = 0; j < 3 && i + j < data.size(); ++j) {
            value = (value << 8) | data[i + j];
            ++count;
        }

        value <<= (3 - count) * 8;

        for (int j = 0; j < 4; ++j) {
            if (j <= count) {
                result += chars[(value >> (18 - j * 6)) & 0x3F];
            } else {
                result += '=';
            }
        }
    }

    return result;
}

}  // namespace mlog
}  // namespace minieye
