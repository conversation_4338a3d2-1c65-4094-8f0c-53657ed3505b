/*
 * Copyright [2025] MINIEYE
 * Description : Log混淆器 - 使用AES128-GCM加密数据，RSA3072加密AES密钥
 * Author      : <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date        : 2025-08-06
 */

#include "LogObfuscator.h"
#include "MiniRSA.h"
#include "EncryptedRSAKeys.h"
#include "AES_GCM.h"
#include <algorithm>
#include <stdexcept>
#include <cstring>
#include <sstream>
#include <random>

namespace minieye {
namespace mlog {

std::string LogObfuscator::obfuscate(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    try {
        // Generate random AES key and IV
        std::vector<uint8_t> aesKey = generateAESKey();
        std::vector<uint8_t> iv = generateIV();

        // Convert input to bytes
        std::vector<uint8_t> plaintext(input.begin(), input.end());

        // Encrypt with AES-GCM
        std::vector<uint8_t> ciphertext, tag;
        if (!AES_GCM::encrypt(plaintext, aesKey, iv, ciphertext, tag)) {
            fprintf(stderr, "DEBUG: AES-GCM encryption failed\n");
            return input; // Encryption failed
        }

        // Parse RSA public key
        std::string publicKeyPEM = getPublicKeyPEM();
        if (publicKeyPEM.empty()) {
            fprintf(stderr, "DEBUG: Public key PEM is empty\n");
            return input;
        }

        RSAKey publicKey = MiniRSA::parseKeyFromPEM(publicKeyPEM, false);

        // Encrypt AES key with RSA
        std::vector<uint8_t> encryptedKey = MiniRSA::encrypt(aesKey, publicKey);

        // Create final package: [encrypted_key_length(4)] + [encrypted_key] + [iv(12)] + [tag(16)] + [ciphertext]
        std::vector<uint8_t> package;

        // Add encrypted key length (4 bytes, big-endian)
        uint32_t keyLen = encryptedKey.size();
        package.push_back((keyLen >> 24) & 0xFF);
        package.push_back((keyLen >> 16) & 0xFF);
        package.push_back((keyLen >> 8) & 0xFF);
        package.push_back(keyLen & 0xFF);

        // Add encrypted key
        package.insert(package.end(), encryptedKey.begin(), encryptedKey.end());

        // Add IV
        package.insert(package.end(), iv.begin(), iv.end());

        // Add tag
        package.insert(package.end(), tag.begin(), tag.end());

        // Add ciphertext
        package.insert(package.end(), ciphertext.begin(), ciphertext.end());

        // Encode to Base64
        return base64Encode(package);

    } catch (const std::exception& e) {
        // If encryption fails, return original input
        fprintf(stderr, "DEBUG: Exception in obfuscate: %s\n", e.what());
        return input;
    }
}

std::string LogObfuscator::deobfuscate(const std::string& input) {
    if (input.empty()) {
        return input;
    }

    try {
        // Decode from Base64
        std::vector<uint8_t> package = base64Decode(input);

        if (package.size() < 4 + AES_IV_SIZE + AES_TAG_SIZE) {
            return input; // Invalid package size
        }

        // Extract encrypted key length (4 bytes, big-endian)
        uint32_t keyLen = (static_cast<uint32_t>(package[0]) << 24) |
                         (static_cast<uint32_t>(package[1]) << 16) |
                         (static_cast<uint32_t>(package[2]) << 8) |
                         static_cast<uint32_t>(package[3]);

        if (package.size() < 4 + keyLen + AES_IV_SIZE + AES_TAG_SIZE) {
            return input; // Invalid package size
        }

        // Extract components
        std::vector<uint8_t> encryptedKey(package.begin() + 4, package.begin() + 4 + keyLen);
        std::vector<uint8_t> iv(package.begin() + 4 + keyLen, package.begin() + 4 + keyLen + AES_IV_SIZE);
        std::vector<uint8_t> tag(package.begin() + 4 + keyLen + AES_IV_SIZE,
                                package.begin() + 4 + keyLen + AES_IV_SIZE + AES_TAG_SIZE);
        std::vector<uint8_t> ciphertext(package.begin() + 4 + keyLen + AES_IV_SIZE + AES_TAG_SIZE,
                                       package.end());

        // Parse RSA private key
        RSAKey privateKey = MiniRSA::parseKeyFromPEM(getPrivateKeyPEM(), true);

        // Decrypt AES key with RSA
        std::vector<uint8_t> aesKey = MiniRSA::decrypt(encryptedKey, privateKey);

        if (aesKey.size() != AES_KEY_SIZE) {
            return input; // Invalid AES key size
        }

        // Decrypt with AES-GCM
        std::vector<uint8_t> plaintext;
        if (!AES_GCM::decrypt(ciphertext, aesKey, iv, tag, plaintext)) {
            return input; // Decryption or authentication failed
        }

        // Convert to string
        return std::string(plaintext.begin(), plaintext.end());

    } catch (const std::exception&) {
        // If deobfuscation fails, return original input
        return input;
    }
}

std::string LogObfuscator::getPrivateKeyPEM() {
    // 临时直接返回明文密钥用于测试
    return PRIVATE_KEY_SOURCE;
}

std::string LogObfuscator::getPublicKeyPEM() {
    // 临时直接返回明文密钥用于测试
    return PUBLIC_KEY_SOURCE;
}

std::vector<uint8_t> LogObfuscator::generateAESKey() {
    std::vector<uint8_t> key(AES_KEY_SIZE);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    for (size_t i = 0; i < AES_KEY_SIZE; i++) {
        key[i] = static_cast<uint8_t>(dis(gen));
    }

    return key;
}

std::vector<uint8_t> LogObfuscator::generateIV() {
    std::vector<uint8_t> iv(AES_IV_SIZE);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    for (size_t i = 0; i < AES_IV_SIZE; i++) {
        iv[i] = static_cast<uint8_t>(dis(gen));
    }

    return iv;
}

std::string LogObfuscator::base64Encode(const std::vector<uint8_t>& data) {
    static const char chars[] =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    std::string result;
    result.reserve(((data.size() + 2) / 3) * 4);

    for (size_t i = 0; i < data.size(); i += 3) {
        uint32_t value = 0;
        int count = 0;

        for (int j = 0; j < 3 && i + j < data.size(); ++j) {
            value = (value << 8) | data[i + j];
            ++count;
        }

        value <<= (3 - count) * 8;

        for (int j = 0; j < 4; ++j) {
            if (j <= count) {
                result += chars[(value >> (18 - j * 6)) & 0x3F];
            } else {
                result += '=';
            }
        }
    }

    return result;
}

std::vector<uint8_t> LogObfuscator::base64Decode(const std::string& input) {
    static const uint8_t decode_table[256] = {
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 62, 64, 64, 64, 63,
        52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 64, 64, 64, 64, 64, 64,
        64,  0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14,
        15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 64, 64, 64, 64, 64,
        64, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
        41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64
    };

    std::vector<uint8_t> result;
    result.reserve((input.length() * 3) / 4);

    uint32_t accumulator = 0;
    int bits = 0;

    for (char c : input) {
        if (c == '=') break; // Padding

        uint8_t value = decode_table[static_cast<uint8_t>(c)];
        if (value == 64) continue; // Invalid character

        accumulator = (accumulator << 6) | value;
        bits += 6;

        if (bits >= 8) {
            bits -= 8;
            result.push_back((accumulator >> bits) & 0xFF);
        }
    }

    return result;
}

}  // namespace mlog
}  // namespace minieye
