/*
 * Copyright [2025] MINIEYE
 * Description : RSA算法轻量化实现，基于bearSSL
 * Author      : <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date        : 2025-08-06
 */

#include "MiniRSA.h"
#include <algorithm>
#include <random>
#include <sstream>
#include <iomanip>
#include <cstring>

namespace minieye {
namespace mlog {

// BigNum implementation
BigNum::BigNum(uint32_t val) : negative(false) {
    if (val != 0) {
        data.push_back(val);
    }
}

BigNum::BigNum(const std::string& hex) : negative(false) {
    // Parse hex string to BigNum
    std::string cleanHex = hex;
    if (cleanHex.substr(0, 2) == "0x" || cleanHex.substr(0, 2) == "0X") {
        cleanHex = cleanHex.substr(2);
    }
    
    // Pad to multiple of 8 characters (32 bits)
    while (cleanHex.length() % 8 != 0) {
        cleanHex = "0" + cleanHex;
    }
    
    // Parse from most significant to least significant
    for (int i = cleanHex.length() - 8; i >= 0; i -= 8) {
        std::string chunk = cleanHex.substr(i, 8);
        uint32_t val = std::stoul(chunk, nullptr, 16);
        data.push_back(val);
    }
    
    normalize();
}

void BigNum::normalize() {
    // Remove leading zeros
    while (!data.empty() && data.back() == 0) {
        data.pop_back();
    }
    if (data.empty()) {
        negative = false;
    }
}

BigNum BigNum::operator+(const BigNum& other) const {
    if (negative != other.negative) {
        // Different signs, use subtraction
        BigNum temp = other;
        temp.negative = !temp.negative;
        return *this - temp;
    }
    
    BigNum result;
    result.negative = negative;
    
    size_t maxSize = std::max(data.size(), other.data.size());
    result.data.resize(maxSize + 1, 0);
    
    uint64_t carry = 0;
    for (size_t i = 0; i < maxSize; ++i) {
        uint64_t sum = carry;
        if (i < data.size()) sum += data[i];
        if (i < other.data.size()) sum += other.data[i];
        
        result.data[i] = static_cast<uint32_t>(sum & 0xFFFFFFFF);
        carry = sum >> 32;
    }
    
    if (carry) {
        result.data[maxSize] = static_cast<uint32_t>(carry);
    }
    
    result.normalize();
    return result;
}

BigNum BigNum::operator-(const BigNum& other) const {
    if (negative != other.negative) {
        // Different signs, use addition
        BigNum temp = other;
        temp.negative = !temp.negative;
        return *this + temp;
    }
    
    // Same signs, need to determine which is larger
    bool thisLarger = !(*this < other);
    const BigNum* larger = thisLarger ? this : &other;
    const BigNum* smaller = thisLarger ? &other : this;
    
    BigNum result;
    result.negative = thisLarger ? negative : !negative;
    result.data.resize(larger->data.size(), 0);
    
    int64_t borrow = 0;
    for (size_t i = 0; i < larger->data.size(); ++i) {
        int64_t diff = static_cast<int64_t>(larger->data[i]) - borrow;
        if (i < smaller->data.size()) {
            diff -= smaller->data[i];
        }
        
        if (diff < 0) {
            diff += 0x100000000LL;
            borrow = 1;
        } else {
            borrow = 0;
        }
        
        result.data[i] = static_cast<uint32_t>(diff);
    }
    
    result.normalize();
    return result;
}

BigNum BigNum::operator*(const BigNum& other) const {
    BigNum result;
    result.negative = (negative != other.negative);

    if (data.empty() || other.data.empty()) {
        return result;
    }

    // Use Karatsuba for large numbers, traditional for small ones
    const size_t KARATSUBA_THRESHOLD = 32;
    if (data.size() >= KARATSUBA_THRESHOLD && other.data.size() >= KARATSUBA_THRESHOLD) {
        result = karatsubaMultiply(*this, other);
        result.negative = (negative != other.negative);
    } else {
        result = traditionalMultiply(*this, other);
        result.negative = (negative != other.negative);
    }

    result.normalize();
    return result;
}

bool BigNum::operator<(const BigNum& other) const {
    if (negative != other.negative) {
        return negative;
    }
    
    if (data.size() != other.data.size()) {
        return negative ? (data.size() > other.data.size()) : (data.size() < other.data.size());
    }
    
    for (int i = data.size() - 1; i >= 0; --i) {
        if (data[i] != other.data[i]) {
            return negative ? (data[i] > other.data[i]) : (data[i] < other.data[i]);
        }
    }
    
    return false; // Equal
}

bool BigNum::operator>(const BigNum& other) const {
    return other < *this;
}

bool BigNum::operator==(const BigNum& other) const {
    return negative == other.negative && data == other.data;
}

std::string BigNum::toHex() const {
    if (data.empty()) {
        return "0";
    }
    
    std::stringstream ss;
    if (negative) ss << "-";
    
    ss << std::hex << data.back();
    for (int i = data.size() - 2; i >= 0; --i) {
        ss << std::setfill('0') << std::setw(8) << data[i];
    }
    
    return ss.str();
}

std::vector<uint8_t> BigNum::toBytes() const {
    if (data.empty()) {
        return {0};
    }
    
    std::vector<uint8_t> result;
    
    // Convert from little-endian uint32_t array to big-endian byte array
    for (int i = data.size() - 1; i >= 0; --i) {
        uint32_t val = data[i];
        result.push_back((val >> 24) & 0xFF);
        result.push_back((val >> 16) & 0xFF);
        result.push_back((val >> 8) & 0xFF);
        result.push_back(val & 0xFF);
    }
    
    // Remove leading zeros
    while (result.size() > 1 && result[0] == 0) {
        result.erase(result.begin());
    }
    
    return result;
}

BigNum BigNum::fromBytes(const std::vector<uint8_t>& bytes) {
    BigNum result;
    
    if (bytes.empty()) {
        return result;
    }
    
    // Convert from big-endian byte array to little-endian uint32_t array
    size_t numWords = (bytes.size() + 3) / 4;
    result.data.resize(numWords, 0);
    
    for (size_t i = 0; i < bytes.size(); ++i) {
        size_t bytePos = bytes.size() - 1 - i;
        size_t wordIndex = i / 4;
        size_t byteInWord = i % 4;
        
        result.data[wordIndex] |= static_cast<uint32_t>(bytes[bytePos]) << (byteInWord * 8);
    }
    
    result.normalize();
    return result;
}

BigNum BigNum::operator%(const BigNum& other) const {
    BigNum remainder;
    divMod(other, &remainder);
    return remainder;
}

BigNum BigNum::divMod(const BigNum& divisor, BigNum* remainder) const {
    if (divisor.data.empty()) {
        throw std::runtime_error("Division by zero");
    }

    BigNum quotient;
    BigNum rem = *this;
    rem.negative = false;

    BigNum div = divisor;
    div.negative = false;

    if (rem < div) {
        if (remainder) *remainder = rem;
        return quotient;
    }

    // Optimized binary long division algorithm
    // Find the bit length of dividend and divisor
    int dividend_bits = getBitLength(rem);
    int divisor_bits = getBitLength(div);

    if (dividend_bits < divisor_bits) {
        if (remainder) *remainder = rem;
        return quotient;
    }

    int shift = dividend_bits - divisor_bits;
    quotient.data.resize((shift / 32) + 1, 0);

    // Shift divisor to align with most significant bit of dividend
    BigNum shifted_div = leftShift(div, shift);

    for (int i = shift; i >= 0; --i) {
        if (!(rem < shifted_div)) {
            rem = rem - shifted_div;
            quotient.data[i / 32] |= (1U << (i % 32));
        }

        // Right shift the divisor by 1 bit
        shifted_div = rightShift(shifted_div, 1);
    }

    quotient.negative = (negative != divisor.negative);
    quotient.normalize();

    if (remainder) {
        remainder->negative = negative;
        *remainder = rem;
    }

    return quotient;
}

// Optimized modular exponentiation using BearSSL-inspired techniques
BigNum BigNum::modPow(const BigNum& exp, const BigNum& mod) const {
    if (mod.data.empty()) {
        throw std::runtime_error("Modulus cannot be zero");
    }

    // Use sliding window for large exponents
    if (exp.data.size() > 4 || mod.data.size() > 32) {
        return slidingWindowModPow(exp, mod);
    }

    // Simple binary exponentiation for small cases
    BigNum result(1);
    BigNum base = *this % mod;
    BigNum exponent = exp;

    while (!exponent.data.empty()) {
        // If exponent is odd
        if (exponent.data[0] & 1) {
            result = (result * base) % mod;
        }

        // Square the base and halve the exponent
        base = (base * base) % mod;

        // Right shift exponent by 1
        uint32_t carry = 0;
        for (int i = exponent.data.size() - 1; i >= 0; --i) {
            uint32_t newCarry = exponent.data[i] & 1;
            exponent.data[i] = (exponent.data[i] >> 1) | (carry << 31);
            carry = newCarry;
        }
        exponent.normalize();
    }

    return result;
}

// RSA implementation
std::vector<uint8_t> MiniRSA::encrypt(const std::vector<uint8_t>& data, const RSAKey& key) {
    if (data.empty()) {
        return {};
    }

    // Apply PKCS#1 v1.5 padding
    size_t keySize = (key.n.toBytes().size());
    std::vector<uint8_t> padded = pkcs1Pad(data, keySize, 2); // Type 2 for encryption

    // Convert to BigNum
    BigNum message = BigNum::fromBytes(padded);

    // Perform RSA encryption: c = m^e mod n
    BigNum encrypted = message.modPow(key.e, key.n);

    // Convert back to bytes with proper padding
    std::vector<uint8_t> result = encrypted.toBytes();

    // Ensure result is exactly keySize bytes
    while (result.size() < keySize) {
        result.insert(result.begin(), 0);
    }

    return result;
}

std::vector<uint8_t> MiniRSA::decrypt(const std::vector<uint8_t>& data, const RSAKey& key) {
    if (data.empty() || !key.hasPrivateKey()) {
        return {};
    }

    // Convert to BigNum
    BigNum ciphertext = BigNum::fromBytes(data);

    // Perform RSA decryption: m = c^d mod n
    BigNum decrypted = ciphertext.modPow(key.d, key.n);

    // Convert back to bytes
    std::vector<uint8_t> padded = decrypted.toBytes();

    // Ensure proper size
    size_t keySize = key.n.toBytes().size();
    while (padded.size() < keySize) {
        padded.insert(padded.begin(), 0);
    }

    // Remove PKCS#1 v1.5 padding
    return pkcs1Unpad(padded, 2); // Type 2 for encryption
}

std::vector<uint8_t> MiniRSA::pkcs1Pad(const std::vector<uint8_t>& data, size_t keySize, int type) {
    if (data.size() > keySize - 11) {
        throw std::runtime_error("Data too large for key size");
    }

    std::vector<uint8_t> result(keySize);
    result[0] = 0x00;
    result[1] = static_cast<uint8_t>(type);

    size_t padLen = keySize - data.size() - 3;

    if (type == 1) {
        // Type 1: padding with 0xFF
        std::fill(result.begin() + 2, result.begin() + 2 + padLen, 0xFF);
    } else if (type == 2) {
        // Type 2: padding with random non-zero bytes
        std::vector<uint8_t> randomBytes = generateRandomBytes(padLen);
        for (size_t i = 0; i < padLen; ++i) {
            // Ensure non-zero
            if (randomBytes[i] == 0) randomBytes[i] = 1;
            result[2 + i] = randomBytes[i];
        }
    }

    result[2 + padLen] = 0x00;
    std::copy(data.begin(), data.end(), result.begin() + 3 + padLen);

    return result;
}

std::vector<uint8_t> MiniRSA::pkcs1Unpad(const std::vector<uint8_t>& data, int type) {
    if (data.size() < 11 || data[0] != 0x00 || data[1] != type) {
        throw std::runtime_error("Invalid PKCS#1 padding");
    }

    size_t separatorPos = 2;
    while (separatorPos < data.size() && data[separatorPos] != 0x00) {
        separatorPos++;
    }

    if (separatorPos >= data.size()) {
        throw std::runtime_error("Invalid PKCS#1 padding: no separator found");
    }

    return std::vector<uint8_t>(data.begin() + separatorPos + 1, data.end());
}

std::vector<uint8_t> MiniRSA::generateRandomBytes(size_t size) {
    std::vector<uint8_t> result(size);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1, 255);

    for (size_t i = 0; i < size; ++i) {
        result[i] = static_cast<uint8_t>(dis(gen));
    }

    return result;
}

// Optimized multiplication algorithms
BigNum traditionalMultiply(const BigNum& a, const BigNum& b) {
    BigNum result;

    if (a.data.empty() || b.data.empty()) {
        return result;
    }

    result.data.resize(a.data.size() + b.data.size(), 0);

    for (size_t i = 0; i < a.data.size(); ++i) {
        uint64_t carry = 0;
        for (size_t j = 0; j < b.data.size(); ++j) {
            uint64_t prod = static_cast<uint64_t>(a.data[i]) * b.data[j] +
                           result.data[i + j] + carry;
            result.data[i + j] = static_cast<uint32_t>(prod & 0xFFFFFFFF);
            carry = prod >> 32;
        }
        if (carry) {
            result.data[i + b.data.size()] += static_cast<uint32_t>(carry);
        }
    }

    result.normalize();
    return result;
}

BigNum karatsubaMultiply(const BigNum& a, const BigNum& b) {
    // Base case: use traditional multiplication for small numbers
    if (a.data.size() <= 32 || b.data.size() <= 32) {
        return traditionalMultiply(a, b);
    }

    // Make both numbers the same size by padding with zeros
    size_t maxSize = std::max(a.data.size(), b.data.size());
    size_t halfSize = (maxSize + 1) / 2;

    // Split a = a1 * B^halfSize + a0
    // Split b = b1 * B^halfSize + b0
    BigNum a0, a1, b0, b1;

    // Extract lower half (a0, b0)
    a0.data.assign(a.data.begin(), a.data.begin() + std::min(halfSize, a.data.size()));
    b0.data.assign(b.data.begin(), b.data.begin() + std::min(halfSize, b.data.size()));

    // Extract upper half (a1, b1)
    if (a.data.size() > halfSize) {
        a1.data.assign(a.data.begin() + halfSize, a.data.end());
    }
    if (b.data.size() > halfSize) {
        b1.data.assign(b.data.begin() + halfSize, b.data.end());
    }

    a0.normalize();
    a1.normalize();
    b0.normalize();
    b1.normalize();

    // Recursive calls
    BigNum z0 = karatsubaMultiply(a0, b0);  // a0 * b0
    BigNum z2 = karatsubaMultiply(a1, b1);  // a1 * b1

    // Calculate (a0 + a1) * (b0 + b1)
    BigNum a_sum = a0 + a1;
    BigNum b_sum = b0 + b1;
    BigNum z1_temp = karatsubaMultiply(a_sum, b_sum);

    // z1 = (a0 + a1) * (b0 + b1) - z0 - z2
    BigNum z1 = z1_temp - z0 - z2;

    // Result = z2 * B^(2*halfSize) + z1 * B^halfSize + z0
    BigNum result = z0;

    // Add z1 * B^halfSize
    if (!z1.data.empty()) {
        if (result.data.size() < halfSize + z1.data.size()) {
            result.data.resize(halfSize + z1.data.size(), 0);
        }
        for (size_t i = 0; i < z1.data.size(); ++i) {
            uint64_t sum = static_cast<uint64_t>(result.data[halfSize + i]) + z1.data[i];
            result.data[halfSize + i] = static_cast<uint32_t>(sum & 0xFFFFFFFF);
            if (sum >> 32) {
                // Handle carry
                size_t carry_pos = halfSize + i + 1;
                while (carry_pos < result.data.size() && result.data[carry_pos] == 0xFFFFFFFF) {
                    result.data[carry_pos] = 0;
                    carry_pos++;
                }
                if (carry_pos >= result.data.size()) {
                    result.data.push_back(1);
                } else {
                    result.data[carry_pos]++;
                }
            }
        }
    }

    // Add z2 * B^(2*halfSize)
    if (!z2.data.empty()) {
        size_t shift = 2 * halfSize;
        if (result.data.size() < shift + z2.data.size()) {
            result.data.resize(shift + z2.data.size(), 0);
        }
        for (size_t i = 0; i < z2.data.size(); ++i) {
            uint64_t sum = static_cast<uint64_t>(result.data[shift + i]) + z2.data[i];
            result.data[shift + i] = static_cast<uint32_t>(sum & 0xFFFFFFFF);
            if (sum >> 32) {
                // Handle carry
                size_t carry_pos = shift + i + 1;
                while (carry_pos < result.data.size() && result.data[carry_pos] == 0xFFFFFFFF) {
                    result.data[carry_pos] = 0;
                    carry_pos++;
                }
                if (carry_pos >= result.data.size()) {
                    result.data.push_back(1);
                } else {
                    result.data[carry_pos]++;
                }
            }
        }
    }

    result.normalize();
    return result;
}

// Helper functions for optimized division
int getBitLength(const BigNum& num) {
    if (num.data.empty()) {
        return 0;
    }

    int bits = (num.data.size() - 1) * 32;
    uint32_t msw = num.data.back();

    // Count bits in most significant word
    while (msw > 0) {
        bits++;
        msw >>= 1;
    }

    return bits;
}

BigNum leftShift(const BigNum& num, int bits) {
    if (bits <= 0 || num.data.empty()) {
        return num;
    }

    BigNum result;
    int word_shift = bits / 32;
    int bit_shift = bits % 32;

    result.data.resize(num.data.size() + word_shift + (bit_shift > 0 ? 1 : 0), 0);

    if (bit_shift == 0) {
        // Simple word shift
        for (size_t i = 0; i < num.data.size(); ++i) {
            result.data[i + word_shift] = num.data[i];
        }
    } else {
        // Bit shift with carry
        uint32_t carry = 0;
        for (size_t i = 0; i < num.data.size(); ++i) {
            uint32_t val = num.data[i];
            result.data[i + word_shift] = (val << bit_shift) | carry;
            carry = val >> (32 - bit_shift);
        }
        if (carry) {
            result.data[num.data.size() + word_shift] = carry;
        }
    }

    result.normalize();
    return result;
}

BigNum rightShift(const BigNum& num, int bits) {
    if (bits <= 0 || num.data.empty()) {
        return num;
    }

    BigNum result;
    int word_shift = bits / 32;
    int bit_shift = bits % 32;

    if (word_shift >= static_cast<int>(num.data.size())) {
        return result; // Result is zero
    }

    result.data.resize(num.data.size() - word_shift, 0);

    if (bit_shift == 0) {
        // Simple word shift
        for (size_t i = word_shift; i < num.data.size(); ++i) {
            result.data[i - word_shift] = num.data[i];
        }
    } else {
        // Bit shift with borrow
        uint32_t borrow = 0;
        for (int i = num.data.size() - 1; i >= word_shift; --i) {
            uint32_t val = num.data[i];
            result.data[i - word_shift] = (val >> bit_shift) | borrow;
            borrow = val << (32 - bit_shift);
        }
    }

    result.normalize();
    return result;
}

// 基于bearssl优化蒙哥马利模态指数
BigNum BigNum::montgomeryModPow(const BigNum& exp, const BigNum& mod) const {
    if (mod.data.empty() || (mod.data[0] & 1) == 0) {
        // Montgomery requires odd modulus, fall back to regular method
        return slidingWindowModPow(exp, mod);
    }

    // Compute Montgomery inverse: m0i = -1/m mod 2^32
    uint32_t m0i = computeMontyInverse(mod.data[0]);

    // Convert base to Montgomery form
    BigNum base_mont = (*this % mod).toMontgomery(mod);
    BigNum result_mont = BigNum(1).toMontgomery(mod);

    // Binary exponentiation in Montgomery domain
    for (int i = exp.data.size() - 1; i >= 0; --i) {
        uint32_t word = exp.data[i];
        for (int bit = 31; bit >= 0; --bit) {
            // Square
            result_mont = result_mont.montgomeryMul(result_mont, mod, m0i);

            // Multiply if bit is set
            if (word & (1U << bit)) {
                result_mont = result_mont.montgomeryMul(base_mont, mod, m0i);
            }
        }
    }

    // Convert back from Montgomery form
    return result_mont.fromMontgomery(mod, m0i);
}

// 滑动窗口预计算优化
BigNum BigNum::slidingWindowModPow(const BigNum& exp, const BigNum& mod) const {
    if (mod.data.empty()) {
        throw std::runtime_error("Modulus cannot be zero");
    }

    // For now, use a simpler optimized binary method
    // This avoids the complexity of sliding window while still being faster
    const int window_size = 3; // 3-bit window for simplicity
    const int table_size = 1 << window_size; // 8 entries

    // Precompute odd powers: base^1, base^3, base^5, base^7
    std::vector<BigNum> table(table_size);
    BigNum base = *this % mod;
    table[1] = base;

    if (table_size > 2) {
        BigNum base_squared = (base * base) % mod;
        for (int i = 3; i < table_size; i += 2) {
            table[i] = (table[i-2] * base_squared) % mod;
        }
    }

    BigNum result(1);

    // Process exponent from most significant bit to least
    // Skip leading zeros in the most significant word
    bool started = false;

    for (int i = exp.data.size() - 1; i >= 0; --i) {
        uint32_t word = exp.data[i];

        for (int bit = 31; bit >= 0; --bit) {
            uint32_t bit_val = (word >> bit) & 1;

            if (!started && bit_val == 0) {
                continue; // Skip leading zeros
            }
            started = true;

            result = (result * result) % mod;

            if (bit_val) {
                result = (result * base) % mod;
            }
        }
    }

    return result;
}

// Compute Montgomery inverse: -1/m mod 2^32
uint32_t BigNum::computeMontyInverse(uint32_t m0) {
    // Extended Euclidean algorithm for computing modular inverse
    uint32_t y = 1;
    for (int i = 0; i < 31; ++i) {
        if ((m0 * y) & 1) {
            y += 1U << (i + 1);
        }
    }
    return ~y + 1; // Return -y mod 2^32
}

// Convert to Montgomery form: a * R mod m, where R = 2^(32*len)
BigNum BigNum::toMontgomery(const BigNum& mod) const {
    // R = 2^(32 * mod.data.size())
    BigNum R(1);
    R = leftShift(R, 32 * mod.data.size());
    return (*this * R) % mod;
}

// Convert from Montgomery form: a / R mod m
BigNum BigNum::fromMontgomery(const BigNum& mod, uint32_t m0i) const {
    // This is a simplified version - full implementation would use
    // Montgomery reduction algorithm
    BigNum R(1);
    R = leftShift(R, 32 * mod.data.size());

    // Find R^-1 mod m using extended Euclidean algorithm
    BigNum R_inv = R.modInverse(mod);
    return (*this * R_inv) % mod;
}

// Montgomery multiplication: (a * b) / R mod m
BigNum BigNum::montgomeryMul(const BigNum& other, const BigNum& mod, uint32_t m0i) const {
    // Simplified Montgomery multiplication
    // Full implementation would use the optimized algorithm from BearSSL
    BigNum product = (*this * other);

    // Montgomery reduction step (simplified)
    BigNum R(1);
    R = leftShift(R, 32 * mod.data.size());

    // This is not the full Montgomery reduction, but a placeholder
    // The real algorithm would avoid the division
    return (product % mod);
}

// Helper function to get bit at position
bool BigNum::getBit(const BigNum& num, int pos) {
    if (pos < 0) return false;
    size_t word_idx = pos / 32;
    int bit_idx = pos % 32;
    if (word_idx >= num.data.size()) return false;
    return (num.data[word_idx] >> bit_idx) & 1;
}

// Helper function to compute modular inverse using extended Euclidean algorithm
BigNum BigNum::modInverse(const BigNum& mod) const {
    BigNum a = *this % mod;
    BigNum m = mod;
    BigNum x0(0), x1(1);

    if (m == BigNum(1)) return BigNum(0);

    while (a > BigNum(1)) {
        BigNum remainder;
        BigNum q = a.divMod(m, &remainder);
        BigNum t = m;

        m = remainder;
        a = t;
        t = x0;

        x0 = x1 - q * x0;
        x1 = t;
    }

    if (x1.negative) x1 = x1 + mod;
    return x1;
}

// Base64 decode implementation
std::vector<uint8_t> MiniRSA::base64Decode(const std::string& input) {
    static const uint8_t decode_table[256] = {
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 62, 64, 64, 64, 63,
        52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 64, 64, 64, 64, 64, 64,
        64,  0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14,
        15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 64, 64, 64, 64, 64,
        64, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
        41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64
    };

    std::vector<uint8_t> result;
    result.reserve((input.length() * 3) / 4);

    uint32_t accumulator = 0;
    int bits = 0;

    for (char c : input) {
        if (c == '=') break; // Padding

        uint8_t value = decode_table[static_cast<uint8_t>(c)];
        if (value == 64) continue; // Invalid character

        accumulator = (accumulator << 6) | value;
        bits += 6;

        if (bits >= 8) {
            bits -= 8;
            result.push_back((accumulator >> bits) & 0xFF);
        }
    }

    return result;
}

// Simple PEM parser for RSA keys
RSAKey MiniRSA::parseKeyFromPEM(const std::string& pem, bool isPrivate) {
    RSAKey key;

    // Find the base64 content between BEGIN and END markers
    std::string beginMarker = isPrivate ? "-----BEGIN RSA PRIVATE KEY-----" : "-----BEGIN PUBLIC KEY-----";
    std::string endMarker = isPrivate ? "-----END RSA PRIVATE KEY-----" : "-----END PUBLIC KEY-----";

    size_t beginPos = pem.find(beginMarker);
    size_t endPos = pem.find(endMarker);

    if (beginPos == std::string::npos || endPos == std::string::npos) {
        throw std::runtime_error("Invalid PEM format");
    }

    beginPos += beginMarker.length();
    std::string base64Content = pem.substr(beginPos, endPos - beginPos);

    // Remove whitespace and newlines
    base64Content.erase(std::remove_if(base64Content.begin(), base64Content.end(),
                                      [](char c) { return std::isspace(c); }),
                       base64Content.end());

    // Decode base64
    std::vector<uint8_t> der = base64Decode(base64Content);

    // Parse ASN.1 DER structure
    return parseASN1(der, isPrivate);
}

// Simple ASN.1 parser for RSA keys
RSAKey MiniRSA::parseASN1(const std::vector<uint8_t>& der, bool isPrivate) {
    RSAKey key;
    size_t pos = 0;

    if (der.empty()) {
        throw std::runtime_error("Empty DER data");
    }

    // Skip outer SEQUENCE
    if (der[pos] != 0x30) {
        throw std::runtime_error("Invalid ASN.1 structure");
    }
    pos = skipASN1Sequence(der, pos);

    if (isPrivate) {
        // Private key format: SEQUENCE { version, n, e, d, p, q, dp, dq, qinv }
        // Skip version
        pos = skipASN1Integer(der, pos);

        // Parse n (modulus)
        key.n = parseASN1Integer(der, pos);

        // Parse e (public exponent)
        key.e = parseASN1Integer(der, pos);

        // Parse d (private exponent)
        key.d = parseASN1Integer(der, pos);

        // Parse p, q, dp, dq, qinv (optional CRT parameters)
        if (pos < der.size()) {
            key.p = parseASN1Integer(der, pos);
            key.q = parseASN1Integer(der, pos);
            key.dp = parseASN1Integer(der, pos);
            key.dq = parseASN1Integer(der, pos);
            key.qinv = parseASN1Integer(der, pos);
        }
    } else {
        // Public key format: SEQUENCE { algorithm, publicKey }
        // Skip algorithm identifier SEQUENCE
        if (der[pos] != 0x30) {
            throw std::runtime_error("Expected algorithm SEQUENCE");
        }
        pos++; // Skip SEQUENCE tag

        // Skip algorithm sequence length
        size_t algLen = der[pos++];
        if (algLen & 0x80) {
            size_t lenBytes = algLen & 0x7F;
            algLen = 0;
            for (size_t i = 0; i < lenBytes; i++) {
                algLen = (algLen << 8) | der[pos++];
            }
        }
        pos += algLen; // Skip entire algorithm identifier

        // Parse BIT STRING containing the actual public key
        if (pos >= der.size() || der[pos] != 0x03) {
            throw std::runtime_error("Expected BIT STRING");
        }
        pos++; // Skip tag

        // Parse BIT STRING length
        size_t len = der[pos++];
        if (len & 0x80) {
            size_t lenBytes = len & 0x7F;
            len = 0;
            for (size_t i = 0; i < lenBytes; i++) {
                len = (len << 8) | der[pos++];
            }
        }

        pos++; // Skip unused bits byte

        // Now parse the inner SEQUENCE { n, e }
        if (pos >= der.size() || der[pos] != 0x30) {
            throw std::runtime_error("Expected SEQUENCE in public key");
        }
        pos++; // Skip SEQUENCE tag

        // Skip inner sequence length
        size_t innerLen = der[pos++];
        if (innerLen & 0x80) {
            size_t lenBytes = innerLen & 0x7F;
            innerLen = 0;
            for (size_t i = 0; i < lenBytes; i++) {
                innerLen = (innerLen << 8) | der[pos++];
            }
        }

        // Parse n (modulus)
        key.n = parseASN1Integer(der, pos);

        // Parse e (public exponent)
        key.e = parseASN1Integer(der, pos);
    }

    return key;
}

BigNum MiniRSA::parseASN1Integer(const std::vector<uint8_t>& der, size_t& pos) {
    if (pos >= der.size() || der[pos] != 0x02) {
        throw std::runtime_error("Expected INTEGER tag");
    }
    pos++; // Skip tag

    // Parse length
    size_t len = der[pos++];
    if (len & 0x80) {
        size_t lenBytes = len & 0x7F;
        len = 0;
        for (size_t i = 0; i < lenBytes; i++) {
            len = (len << 8) | der[pos++];
        }
    }

    // Skip leading zero if present (for positive integers)
    if (len > 0 && der[pos] == 0x00) {
        pos++;
        len--;
    }

    // Extract integer bytes
    std::vector<uint8_t> intBytes(der.begin() + pos, der.begin() + pos + len);
    pos += len;

    return BigNum::fromBytes(intBytes);
}

size_t MiniRSA::skipASN1Integer(const std::vector<uint8_t>& der, size_t pos) {
    if (pos >= der.size() || der[pos] != 0x02) {
        throw std::runtime_error("Expected INTEGER tag");
    }
    pos++; // Skip tag

    // Parse length
    size_t len = der[pos++];
    if (len & 0x80) {
        size_t lenBytes = len & 0x7F;
        len = 0;
        for (size_t i = 0; i < lenBytes; i++) {
            len = (len << 8) | der[pos++];
        }
    }

    return pos + len;
}

size_t MiniRSA::skipASN1Sequence(const std::vector<uint8_t>& der, size_t pos) {
    if (pos >= der.size() || der[pos] != 0x30) {
        throw std::runtime_error("Expected SEQUENCE tag");
    }
    pos++; // Skip tag

    // Parse length
    size_t len = der[pos++];
    if (len & 0x80) {
        size_t lenBytes = len & 0x7F;
        len = 0;
        for (size_t i = 0; i < lenBytes; i++) {
            len = (len << 8) | der[pos++];
        }
    }

    return pos; // Return position after length, pointing to content
}

}  // namespace mlog
}  // namespace minieye
