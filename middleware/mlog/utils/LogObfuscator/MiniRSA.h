/*
 * Copyright [2025] MINIEYE
 * Description : RSA算法轻量化实现，基于bearSSL
 * Author      : <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date        : 2025-08-06
 */

#pragma once

#include <string>
#include <vector>
#include <cstdint>

namespace minieye {
namespace mlog {

struct BigNum {
    std::vector<uint32_t> data;
    bool negative;
    
    BigNum() : negative(false) {}
    explicit BigNum(uint32_t val);
    BigNum(const std::string& hex);
    
    BigNum operator+(const BigNum& other) const;
    BigNum operator-(const BigNum& other) const;
    BigNum operator*(const BigNum& other) const;
    BigNum operator%(const BigNum& other) const;
    bool operator<(const BigNum& other) const;
    bool operator>(const BigNum& other) const;
    bool operator==(const BigNum& other) const;
    
    BigNum modPow(const BigNum& exp, const BigNum& mod) const;
    std::string toHex() const;
    std::vector<uint8_t> toBytes() const;
    static BigNum fromBytes(const std::vector<uint8_t>& bytes);

    friend BigNum traditionalMultiply(const BigNum& a, const BigNum& b);
    friend BigNum karatsubaMultiply(const BigNum& a, const BigNum& b);
    friend int getBitLength(const BigNum& num);
    friend BigNum leftShift(const BigNum& num, int bits);
    friend BigNum rightShift(const BigNum& num, int bits);

    BigNum montgomeryModPow(const BigNum& exp, const BigNum& mod) const;
    BigNum slidingWindowModPow(const BigNum& exp, const BigNum& mod) const;

private:
    void normalize();
    BigNum divMod(const BigNum& divisor, BigNum* remainder = nullptr) const;

    static uint32_t computeMontyInverse(uint32_t m0);
    BigNum toMontgomery(const BigNum& mod) const;
    BigNum fromMontgomery(const BigNum& mod, uint32_t m0i) const;
    BigNum montgomeryMul(const BigNum& other, const BigNum& mod, uint32_t m0i) const;
    BigNum modInverse(const BigNum& mod) const;

    static bool getBit(const BigNum& num, int pos);
};

/**
 * @brief RSA key structure
 */
struct RSAKey {
    BigNum n;  // modulus
    BigNum e;  // public exponent
    BigNum d;  // private exponent
    
    // Optional CRT parameters for faster decryption
    BigNum p, q;      // prime factors
    BigNum dp, dq;    // d mod (p-1), d mod (q-1)
    BigNum qinv;      // q^-1 mod p
    
    bool hasPrivateKey() const { return !d.data.empty(); }
    bool hasCRTParams() const { return !p.data.empty() && !q.data.empty(); }
};


class MiniRSA {
public:
    static std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, const RSAKey& key);
    static std::vector<uint8_t> decrypt(const std::vector<uint8_t>& data, const RSAKey& key);
    static RSAKey parseKeyFromPEM(const std::string& pem, bool isPrivate);
    static std::vector<uint8_t> pkcs1Pad(const std::vector<uint8_t>& data, size_t keySize, int type);
    static std::vector<uint8_t> pkcs1Unpad(const std::vector<uint8_t>& data, int type);
    static std::vector<uint8_t> base64Decode(const std::string& input);

private:
    static std::vector<uint8_t> generateRandomBytes(size_t size);
    static RSAKey parseASN1(const std::vector<uint8_t>& der, bool isPrivate);
    static BigNum parseASN1Integer(const std::vector<uint8_t>& der, size_t& pos);
    static size_t skipASN1Integer(const std::vector<uint8_t>& der, size_t pos);
    static size_t skipASN1Sequence(const std::vector<uint8_t>& der, size_t pos);
};

BigNum traditionalMultiply(const BigNum& a, const BigNum& b);
BigNum karatsubaMultiply(const BigNum& a, const BigNum& b);
int getBitLength(const BigNum& num);
BigNum leftShift(const BigNum& num, int bits);
BigNum rightShift(const BigNum& num, int bits);

}  // namespace mlog
}  // namespace minieye
