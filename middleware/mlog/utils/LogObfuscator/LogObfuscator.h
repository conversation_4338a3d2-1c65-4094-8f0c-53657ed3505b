/*
 * Copyright [2025] MINIEYE
 * Description : Log混淆器
 * Author      : x<PERSON><PERSON><PERSON>n
 * Date        : 2025-08-06
 */

#pragma once

#include <string>
#include <vector>
#include <cstdint>

namespace minieye {
namespace mlog {

class LogObfuscator {
 public:
    /**
     * @brief Obfuscate log content using RSA3072 encryption and Base64 encoding
     * @param input The original log content
     * @return Obfuscated string
     */
    static std::string obfuscate(const std::string& input);

    /**
     * @brief Deobfuscate log content
     * @param input The obfuscated log content
     * @return Original string
     */
    static std::string deobfuscate(const std::string& input);

    /**
     * @brief Get RSA public key for encryption
     * @return RSA public key PEM string
     */
    static std::string getPublicKeyPEM();

    /**
     * @brief Get RSA private key for decryption
     * @return RSA private key PEM string
     */
    static std::string getPrivateKeyPEM();

 private:

    /**
     * @brief Split long input into chunks for RSA encryption
     * @param input Input string
     * @param chunkSize Maximum chunk size
     * @return Vector of chunks
     */
    static std::vector<std::string> splitIntoChunks(const std::string& input, size_t chunkSize);

    /**
     * @brief Join encrypted chunks back into single string
     * @param chunks Vector of encrypted chunks
     * @return Joined string
     */
    static std::string joinChunks(const std::vector<std::string>& chunks);

    /**
     * @brief Encode data to Base64
     * @param data Input data
     * @return Base64 encoded string
     */
    static std::string base64Encode(const std::vector<uint8_t>& data);

    // RSA chunk size for 3072-bit key (384 bytes - 11 bytes padding = 373 bytes max)
    static const size_t RSA_CHUNK_SIZE = 300;  // Conservative size
    static const char CHUNK_SEPARATOR = '|';
};

}  // namespace mlog
}  // namespace minieye
